import { useQuery } from "@tanstack/react-query";
import { api } from "~/lib/api";
import { CancelledClassesResponse } from "../types";
import { useSession } from "~/modules/login/auth-provider";

export const fetchCancelledClasses =
  async ({}: {}): Promise<CancelledClassesResponse> => {
    try {
      // For now, return mock data since the API endpoint doesn't exist yet
      // TODO: Replace with actual API call when backend is ready
      const mockData: CancelledClassesResponse = {
        success: true,
        data: [
          {
            id: 1,
            date: "2024-01-15",
            reason: "Instructor illness",
            classes: ["Yoga Class", "Pilates"],
            created_at: "2024-01-15T10:00:00Z",
            updated_at: "2024-01-15T10:00:00Z",
          },
          {
            id: 2,
            date: "2024-01-20",
            reason: "Facility maintenance",
            classes: ["Spin Class"],
            created_at: "2024-01-20T09:00:00Z",
            updated_at: "2024-01-20T09:00:00Z",
          },
        ],
      };

      // Simulate API delay
      await new Promise((resolve) => setTimeout(resolve, 500));

      return mockData;
    } catch (err) {
      throw new Error("Could not fetch cancelled classes");
    }
  };

export const useCancelledClassesQuery = () => {
  const { data: sessionData } = useSession();

  return useQuery({
    queryKey: ["cancelled-classes", sessionData?.token],
    queryFn: async ({ signal }) => fetchCancelledClasses({ signal }),
    select: (data) => data.data || [],
  });
};
