export interface CancelledClass {
  id: number;
  date: string;
  reason: string;
  classes: string[];
  created_at: string;
  updated_at: string;
}

export interface CancelClassRequest {
  date: string;
  class_ids: number[];
  reason: string;
}

export interface CancelledClassesResponse {
  success: boolean;
  data: CancelledClass[];
  message?: string;
}

export interface CancelClassResponse {
  success: boolean;
  message: string;
}
