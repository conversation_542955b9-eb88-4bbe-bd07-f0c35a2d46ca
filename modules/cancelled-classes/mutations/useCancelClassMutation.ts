import { useMutation, useQueryClient } from "@tanstack/react-query";
import { api } from "~/lib/api";
import { showSuccessToast } from "~/components/toast";
import { CancelClassRequest, CancelClassResponse } from "../types";

export const cancelClass = async (
  data: CancelClassRequest
): Promise<CancelClassResponse> => {
  try {
    // For now, return mock success response since the API endpoint doesn't exist yet
    // TODO: Replace with actual API call when backend is ready
    console.log("Mock cancel class request:", data);

    // Simulate API delay
    await new Promise((resolve) => setTimeout(resolve, 1000));

    return {
      success: true,
      message: "Classes cancelled successfully",
    };
  } catch (error) {
    throw new Error("Failed to cancel class");
  }
};

export const useCancelClassMutation = (onSuccess?: () => void) => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: cancelClass,
    onSuccess: async (data) => {
      if (data.success) {
        // Invalidate and refetch cancelled classes
        await queryClient.invalidateQueries({
          queryKey: ["cancelled-classes"],
        });

        // Invalidate classes queries to update the main classes list
        await queryClient.invalidateQueries({
          queryKey: ["classes"],
        });

        showSuccessToast(data.message || "Classes cancelled successfully");
        onSuccess?.();
      }
    },
  });
};
