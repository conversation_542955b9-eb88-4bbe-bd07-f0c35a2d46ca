import { View } from "react-native";
import { <PERSON>, Card<PERSON>ontent, CardHeader } from "~/components/ui/card";
import { Text } from "~/components/ui/text";
import { CancelledClass } from "~/modules/cancelled-classes/types";
import { format, parseISO } from "date-fns";
import MaterialIcons from "@expo/vector-icons/MaterialIcons";

interface CancelledClassCardProps extends CancelledClass {}

export function ContactClassCard({
  date,
  reason,
  classes,
  created_at,
}: CancelledClassCardProps) {
  const formattedDate = format(parseISO(date), "EEE MMM d, yyyy");
  const formattedCreatedAt = format(
    parseISO(created_at),
    "MMM d, yyyy 'at' h:mm a"
  );

  return (
    <Card className="mb-3 bg-red-50 dark:bg-red-900/20 border-red-200 dark:border-red-800">
      <CardHeader className="pb-2">
        <View className="flex-row items-center justify-between">
          <View className="flex-row items-center">
            <MaterialIcons
              name="cancel"
              size={20}
              color="#dc2626"
              style={{ marginRight: 8 }}
            />
            <Text className="font-semibold text-red-700 dark:text-red-300">
              {formattedDate}
            </Text>
          </View>
        </View>
      </CardHeader>
      <CardContent className="pt-0">
        <View className="space-y-2">
          <View>
            <Text className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              Classes:
            </Text>
            <Text className="text-sm text-gray-600 dark:text-gray-400 line-through">
              {classes.join(", ")}
            </Text>
          </View>

          <View>
            <Text className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              Reason:
            </Text>
            <Text className="text-sm text-gray-600 dark:text-gray-400">
              {reason}
            </Text>
          </View>

          <Text className="text-xs text-gray-500 dark:text-gray-500 mt-2">
            Cancelled on {formattedCreatedAt}
          </Text>
        </View>
      </CardContent>
    </Card>
  );
}
