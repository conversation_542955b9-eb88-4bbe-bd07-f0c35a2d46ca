import React from "react";
import { <PERSON>, ScrollView } from "react-native";
import { useForm, useWatch } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { Button } from "~/components/ui/button";
import {
  Form,
  FormField,
  FormLabel,
  FormMessage,
  FormInput,
} from "~/components/ui/form";
import { Text } from "~/components/ui/text";
import { DatePicker } from "~/components/modules/common/date-picker";

import { MultiComboBox } from "~/components/modules/common/Multi-combo-box";
import { KeyboardAwareScrollView } from "react-native-keyboard-controller";
import {
  AutocompleteDropdownContextProvider,
  AutocompleteDropdownItem,
} from "react-native-autocomplete-dropdown";
import { useClassesData } from "~/modules/classes/queries/useClassesQuery";
import { formatClassDate } from "~/modules/classes/utils";
import { TimePicker } from "../common/time-picker";

const CancelClassSchema = z.object({
  date: z.date({
    message: "Date is required",
  }),
  time: z
    .object({
      id: z.string(),
      title: z.string(),
    })
    .nullable()
    .optional(),
  classes: z
    .array(
      z.object({
        id: z.string(),
        title: z.string(),
      })
    )
    .min(1, "At least one class must be selected"),
  message: z.string().min(1, "Reason is required"),
});

export type CancelClassFormValues = z.infer<typeof CancelClassSchema>;

interface CancelClassFormProps {
  onSubmit: (data: CancelClassFormValues) => void;
  isLoading?: boolean;
}

export const ContactClassForm = ({
  onSubmit,
  isLoading,
}: CancelClassFormProps) => {
  const form = useForm<CancelClassFormValues>({
    resolver: zodResolver(CancelClassSchema),
    defaultValues: {
      date: new Date(),
      time: null,
      classes: [],
      message: "",
    },
  });

  const selectedValues = useWatch({
    control: form.control,
  });

  const { data: classesData = [], isPending } = useClassesData({
    date: formatClassDate(selectedValues.date),
  });

  const classOptions: AutocompleteDropdownItem[] = classesData.map((cls) => ({
    id: cls.id.toString(),
    title: `${cls.name} - ${cls.start_time} to ${cls.end_time} (${cls.room_name})`,
  }));

  const resetClassesValue = () => {
    form.setValue("classes", [], { shouldDirty: true });
  };

  return (
    <AutocompleteDropdownContextProvider>
      <View className="flex-1">
        <Form {...form}>
          <KeyboardAwareScrollView
            bottomOffset={10}
            contentContainerStyle={{
              paddingHorizontal: 16,
              paddingTop: 16,
              paddingBottom: 100,
            }}
            className="flex-1"
          >
            <ScrollView className="space-y-4">
              <View className="space-y-4">
                <FormField
                  control={form.control}
                  name="date"
                  render={({ field }) => (
                    <View className="space-y-3">
                      <DatePicker
                        value={field.value}
                        onChange={(date) => {
                          field.onChange(date);
                          resetClassesValue();
                        }}
                        label="Date of class*"
                      />
                      <FormMessage />
                    </View>
                  )}
                />

                <FormField
                  control={form.control}
                  name="classes"
                  render={({ field }) => (
                    <View className="space-y-2">
                      <FormLabel>Classes* (select one or more)</FormLabel>
                      <View style={{ minHeight: 50 }}>
                        <MultiComboBox
                          data={classOptions}
                          placeholder="Select classes to cancel"
                          selectedItems={field.value}
                          onSelect={(items: AutocompleteDropdownItem[]) => {
                            field.onChange(items);
                          }}
                        />
                      </View>
                      <FormMessage />
                      {isPending && (
                        <Text className="text-sm text-gray-500">
                          Loading classes...
                        </Text>
                      )}
                    </View>
                  )}
                />

                <FormField
                  control={form.control}
                  name="message"
                  render={({ field }) => (
                    <View className="space-y-2">
                      <FormLabel>Message*</FormLabel>
                      <FormInput
                        placeholder="Enter message to send to the class participants (Limit: 375 characters)"
                        value={field.value}
                        onChangeText={field.onChange}
                        onChange={field.onChange}
                        onBlur={field.onBlur}
                        name={field.name}
                        multiline={true}
                        numberOfLines={4}
                        style={{
                          height: 100,
                          textAlignVertical: "top",
                        }}
                      />
                      <FormMessage />
                    </View>
                  )}
                />

                <View className="flex flex-row gap-2">
                  <FormField
                    control={form.control}
                    name="date"
                    render={({ field }) => (
                      <View className="w-[50%]">
                        <DatePicker
                          value={field.value}
                          onChange={(date) => {
                            field.onChange(date);
                            resetClassesValue();
                          }}
                          label="Date of class*"
                        />
                        <FormMessage />
                      </View>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="time"
                    render={({ field }) => (
                      <View className="w-[80%]">
                        <TimePicker
                          value={field.value}
                          onChange={field.onChange}
                          label="Time*"
                          direction="up"
                          placeholder="Select time"
                        />
                        <FormMessage />
                      </View>
                    )}
                  />
                </View>

                <View className="pt-4">
                  <Button
                    onPress={form.handleSubmit(onSubmit)}
                    disabled={isLoading}
                    className="w-full"
                    label={isLoading ? "Cancelling..." : "Save"}
                    isLoading={isLoading}
                  />
                </View>
              </View>
            </ScrollView>
          </KeyboardAwareScrollView>
        </Form>
      </View>
    </AutocompleteDropdownContextProvider>
  );
};
