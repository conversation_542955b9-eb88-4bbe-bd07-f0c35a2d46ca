import React from "react";
import { View } from "react-native";
import { router } from "expo-router";

import { useCancelClassMutation } from "~/modules/cancelled-classes/mutations/useCancelClassMutation";
import { ScreenTracker } from "~/components/analytics/ScreenTracker";

import { ContactClassForm } from "~/components/modules/contact-classes/contact-class-form";

export default function ContactClassPage() {
  const { mutate: cancelClass, isPending } = useCancelClassMutation(() => {
    router.back();
  });

  const handleSubmit = () => {};

  const handleGoBack = () => {
    router.back();
  };

  return (
    <ScreenTracker screenName="Create Cancelled Class">
      <View className="flex-1 bg-background">
        <ContactClassForm isLoading={isPending} onSubmit={handleSubmit} />
      </View>
    </ScreenTracker>
  );
}
