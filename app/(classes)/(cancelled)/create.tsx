import React from "react";
import { View } from "react-native";
import { router } from "expo-router";

import { useCancelClassMutation } from "~/modules/cancelled-classes/mutations/useCancelClassMutation";
import { ScreenTracker } from "~/components/analytics/ScreenTracker";

import { CancelClassForm } from "~/components/modules/cancelled-classes/cancel-class-form";

export default function CreateCancelledClassPage() {
  const { mutate: cancelClass, isPending } = useCancelClassMutation(() => {
    router.back();
  });

  const handleSubmit = () => {};

  const handleGoBack = () => {
    router.back();
  };

  return (
    <ScreenTracker screenName="Create Cancelled Class">
      <View className="flex-1 bg-background">
        <CancelClassForm isLoading={isPending} onSubmit={handleSubmit} />
      </View>
    </ScreenTracker>
  );
}
